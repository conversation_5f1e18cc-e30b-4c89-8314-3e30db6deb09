<template>
  <i 
    :class="iconClasses" 
    :aria-hidden="decorative"
    :aria-label="!decorative ? ariaLabel : undefined"
  ></i>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true,
    validator: (value) => value.startsWith('bi-')
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  decorative: {
    type: Boolean,
    default: true
  },
  ariaLabel: {
    type: String,
    default: ''
  }
})

const iconClasses = computed(() => {
  const classes = ['bi', props.name]
  
  // Add size classes if not default
  if (props.size !== 'md') {
    switch (props.size) {
      case 'sm':
        classes.push('fs-6')
        break
      case 'lg':
        classes.push('fs-4')
        break
      case 'xl':
        classes.push('fs-2')
        break
    }
  }
  
  return classes.join(' ')
})
</script>

<style scoped>
/* Custom size adjustments if needed */
.bi.fs-6 {
  font-size: 0.875rem;
}

.bi.fs-4 {
  font-size: 1.5rem;
}

.bi.fs-2 {
  font-size: 2rem;
}
</style>
