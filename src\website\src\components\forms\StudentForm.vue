<template>
  <BaseCard title="Add New Student">
    <form @submit.prevent="handleSubmit">
      <div class="mb-3">
        <input
          v-model="studentName"
          placeholder="Student name (e.g., <PERSON>)"
          class="form-control"
          required
        />
      </div>
      <div class="d-flex gap-2">
        <BaseButton type="submit" variant="purple" icon="bi-person-plus">
          Add Student
        </BaseButton>
        <BaseButton type="button" variant="secondary" icon="bi-x-lg" @click="$emit('cancel')">
          Cancel
        </BaseButton>
      </div>
    </form>
  </BaseCard>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from '../ui/BaseCard.vue'
import BaseButton from '../ui/BaseButton.vue'

const emit = defineEmits(['submit', 'cancel'])

const studentName = ref('')

const handleSubmit = () => {
  if (studentName.value.trim()) {
    emit('submit', studentName.value.trim())
    studentName.value = ''
  }
}
</script>
